import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { Chat4SceneUtils, type Chat4SceneState } from '@/types/chat4-scene'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import { useChatMessagesStore } from './chat-messages'
import { useChatResourcesStore } from './chat-resources'
import { useStoryStore } from './story'
import type { MomentPost, ShowMomentsEventData } from '@/types/moment'

// TTS消息接口
interface TTSMessage {
  id: string
  content: string
  messageId: string
  originalMessage: any
  sceneId: string
  timestamp: number
}

// 弹幕评论接口
export interface Comment {
  id: string
  username: string
  content: string
  type: 'normal' | 'highlight' | 'system' | 'streamer' | 'self' | 'join'
}

// 场景状态接口
interface SceneState {
  currentScene: Chat4SceneState | null
  previousScene: Chat4SceneState | null
  isTransitioning: boolean
  isLoading: boolean
  loadingType: string | null
  error: string | null
}

export const useChat4Store = defineStore('chat4', () => {
  // ==================== 场景状态管理 ====================
  const sceneState = ref<SceneState>({
    currentScene: null,
    previousScene: null,
    isTransitioning: false,
    isLoading: false,
    loadingType: null,
    error: null,
  })

  // ==================== TTS状态管理 ====================
  const ttsState = ref({
    isPlaying: false,
    currentMessage: null as TTSMessage | null,
    currentDisplayMessage: null as any,
    queue: [] as TTSMessage[],
    pendingDisplayMessages: [] as any[],
    lastProcessedMessageId: null as string | null,
    autoSendTimer: null as ReturnType<typeof setTimeout> | null,
    isSceneTransitioning: false, // 新增：场景切换期间暂停TTS播放
  })

  // ==================== 弹幕状态管理 ====================
  const danmakuState = ref({
    liveComments: [] as Comment[],
    danmakuTimers: [] as ReturnType<typeof setTimeout>[],
    debounceTimer: null as ReturnType<typeof setTimeout> | null,
    version: 0, // 版本号，用于强制触发响应式更新
  })

  // ==================== 朋友圈状态管理 ====================
  const momentState = ref({
    posts: [] as MomentPost[],
    isLoading: false,
    error: null as string | null,
    lastUpdated: null as number | null,
    personalizedSignature: null as string | null,
  })

  // ==================== 日记状态管理 ====================
  const diaryState = ref({
    content: null as string | null,
    date: null as string | null,
    emotion: null as string | null,
    status: null as string | null,
    region: null as string | null,
    isLoading: false,
    error: null as string | null,
    lastUpdated: null as number | null,
  })

  // ==================== 用户消息队列 ====================
  const userMessageQueue = ref<string[]>([])

  // ==================== 场景行为菜单 ====================
  const sceneActionsMenu = ref<
    Array<{
      key: string
      label: string
      icon: string
      level?: number
      imageUrl?: string
      requirement?: string
      value?: number
    }>
  >([])

  // ==================== 地图位置管理 ====================
  const mapLocations = ref<
    Array<{
      id: string
      name: string
      description: string
      image: string
      position: { x: number; y: number }
      isLocked: boolean
      unlockCondition?: {
        requiredLevel?: string
        requiredHeartValue?: number
        requiredCoins?: number
      }
      meetupScene: string
      isNew: boolean
    }>
  >([])

  // ==================== 解锁状态管理 ====================
  const unlockState = ref({
    showPaymentConfirmModal: false,
    paymentCoins: 0,
    unlockTag: '',
    requiredHeartValue: 0,
  })

  // ==================== Computed ====================
  const currentScene = computed(() => sceneState.value.currentScene)
  const isLivingScene = computed(() =>
    Chat4SceneUtils.isLivingScene(currentScene.value),
  )
  const isChatRoomScene = computed(() =>
    Chat4SceneUtils.isPhoneScene(currentScene.value),
  )
  const isVideoCallScene = computed(() =>
    Chat4SceneUtils.isVideoCallScene(currentScene.value),
  )
  const isMeetupScene = computed(() =>
    Chat4SceneUtils.isMeetupScene(currentScene.value),
  )
  const isDancingScene = computed(() =>
    Chat4SceneUtils.isDancingScene(currentScene.value),
  )
  const isMonitorScene = computed(() =>
    Chat4SceneUtils.isMonitorScene(currentScene.value),
  )

  // ==================== 场景管理方法 ====================
  const setCurrentScene = (scene: Chat4SceneState | null) => {
    const oldScene = sceneState.value.currentScene
    sceneState.value.previousScene = oldScene
    sceneState.value.currentScene = scene

    // 场景切换时清理旧场景的状态
    if (oldScene && oldScene !== scene) {
      clearSceneSpecificState(oldScene)
    }
  }

  const setSceneTransitioning = (transitioning: boolean) => {
    sceneState.value.isTransitioning = transitioning
  }

  const setSceneLoading = (loading: boolean, type?: string) => {
    sceneState.value.isLoading = loading
    sceneState.value.loadingType = type || null

    // 场景切换期间暂停TTS播放
    ttsState.value.isSceneTransitioning = loading
  }

  const setSceneError = (error: string | null) => {
    sceneState.value.error = error
  }

  // ==================== TTS管理方法 ====================
  const addTTSMessage = (message: any) => {
    if (!message.content?.text || !message.id) {
      console.warn('Invalid message for TTS:', message)
      return
    }

    // 避免重复处理同一条消息
    if (message.id === ttsState.value.lastProcessedMessageId) {
      return
    }

    // 处理文本内容
    const processedText = message.content.text || message.content.html || ''

    const ttsMessage: TTSMessage = {
      id: message.id,
      content: processedText,
      messageId: message.id,
      originalMessage: message,
      sceneId: currentScene.value || 'unknown',
      timestamp: Date.now(),
    }

    ttsState.value.lastProcessedMessageId = message.id

    // 检查是否应该立即播放TTS
    const shouldPlayImmediately =
      !ttsState.value.isPlaying &&
      ttsState.value.queue.length === 0 &&
      !ttsState.value.isSceneTransitioning

    if (shouldPlayImmediately) {
      ttsState.value.currentMessage = ttsMessage
      ttsState.value.currentDisplayMessage = message

      // 将消息添加到 chat-messages store 中
      const chatMessagesStore = useChatMessagesStore()
      console.log(
        'chat4-tts: 📝 Adding message to chatMessagesStore via Chat4 Store:',
        {
          messageId: message.id,
          content: message.content.text?.substring(0, 50),
          currentScene: currentScene.value,
          currentMessageCount: chatMessagesStore.messages.length,
          msg_type: message.msg_type,
          sender_type: message.sender_type,
          isSceneTransitioning: ttsState.value.isSceneTransitioning,
        },
      )
      chatMessagesStore.messages.push(message)
      console.log(
        'chat4-tts: 📝 Message added via Chat4 Store, new count:',
        chatMessagesStore.messages.length,
      )

      // 如果是直播场景，将 actor 消息转换为弹幕
      if (isLivingScene.value) {
        const storyStore = useStoryStore()
        const newComment: Comment = {
          id: message.id,
          username:
            message.sender?.name || storyStore.currentActor?.name || 'Streamer',
          content: message.content.text || message.content.html || '',
          type: 'streamer',
        }
        addLiveComment(newComment)
      }

      playTTSMessage(ttsMessage)
    } else {
      // 否则添加到队列
      console.log(
        'TTS is playing, queue not empty, or scene transitioning, adding to queue:',
        {
          isPlaying: ttsState.value.isPlaying,
          queueLength: ttsState.value.queue.length,
          isSceneTransitioning: ttsState.value.isSceneTransitioning,
          messageId: ttsMessage.id,
        },
      )
      ttsState.value.queue.push(ttsMessage)
      ttsState.value.pendingDisplayMessages.push(message)
    }
  }

  const playTTSMessage = async (message: TTSMessage) => {
    try {
      console.log('Playing TTS for message:', message.id)

      const audioManager = useAudioManager()
      ttsState.value.isPlaying = true

      // 检查是否静音
      if (audioManager.isMuted) {
        console.log('Audio is muted, skipping TTS playback')
        handleTTSPlaybackEnd()
        return
      }

      // 获取角色信息
      const storyStore = useStoryStore()
      const chatResourcesStore = useChatResourcesStore()
      const chatMessagesStore = useChatMessagesStore()
      const actorId =
        chatResourcesStore.currentActorId || storyStore.currentActor?.id

      if (!actorId || !message.content.trim()) {
        console.error('No actor ID or content for TTS')
        handleTTSPlaybackEnd()
        return
      }

      // 准备并播放TTS
      await audioManager.prepareTTS(
        message.content,
        message.messageId,
        actorId,
        chatMessagesStore.voiceConfig.voice_id,
        chatMessagesStore.voiceConfig.provider,
      )

      await audioManager.playTTS(
        message.content,
        message.messageId,
        chatMessagesStore.voiceConfig.voice_id,
        chatMessagesStore.voiceConfig.provider,
      )

      console.log('TTS playback completed for message:', message.id)

      // 播放完成后，检查audioManager状态并同步
      checkTTSPlayingState()
    } catch (error) {
      console.error('Failed to play TTS for message:', message.id, error)
      handleTTSPlaybackEnd()
    }
  }

  // 检查并同步audioManager的TTS播放状态
  const checkTTSPlayingState = () => {
    // 如果场景正在切换，跳过状态检查
    if (ttsState.value.isSceneTransitioning) {
      return
    }

    const audioManager = useAudioManager()

    // 如果audioManager显示没有在播放，但我们的状态显示在播放，则同步状态
    if (!audioManager.state.isPlaying && ttsState.value.isPlaying) {
      console.log(
        'TTS state sync: audioManager stopped but store shows playing, syncing...',
      )
      handleTTSPlaybackEnd()
    }

    // 如果audioManager正在播放不同的消息，也需要同步
    if (
      audioManager.state.isPlaying &&
      audioManager.state.currentPlayingMessageId !==
        ttsState.value.currentMessage?.messageId
    ) {
      console.log(
        'TTS state sync: audioManager playing different message, syncing...',
      )
      handleTTSPlaybackEnd()
    }
  }

  const handleTTSPlaybackEnd = () => {
    // 清除当前播放的消息
    ttsState.value.currentMessage = null
    ttsState.value.isPlaying = false

    // 如果场景正在切换，暂停后续处理
    if (ttsState.value.isSceneTransitioning) {
      console.log('Scene transitioning, deferring TTS queue processing')
      return
    }

    // 显示下一条等待显示的消息
    showNextPendingMessage()

    // 播放队列中的下一条消息
    playNextTTSMessage()

    // 处理用户消息队列（如果没有更多TTS消息要播放）
    if (ttsState.value.queue.length === 0) {
      handleUserMessageQueue()
    }
  }

  const showNextPendingMessage = () => {
    // 如果场景正在切换，不处理等待显示的消息，留给resumeTTSPlayback处理
    if (ttsState.value.isSceneTransitioning) {
      console.log('Scene transitioning, deferring pending message display')
      return
    }

    if (ttsState.value.pendingDisplayMessages.length > 0) {
      const nextMessage = ttsState.value.pendingDisplayMessages.shift()
      if (nextMessage) {
        console.log('Showing next pending message:', nextMessage.id)
        ttsState.value.currentDisplayMessage = nextMessage

        // 将消息添加到 chat-messages store 中
        const chatMessagesStore = useChatMessagesStore()
        console.log(
          'chat4-tts: 📝 Adding pending message to chatMessagesStore via Chat4 Store:',
          {
            messageId: nextMessage.id,
            content: nextMessage.content.text?.substring(0, 50),
            currentScene: currentScene.value,
            currentMessageCount: chatMessagesStore.messages.length,
            msg_type: nextMessage.msg_type,
            sender_type: nextMessage.sender_type,
          },
        )
        chatMessagesStore.messages.push(nextMessage)
        console.log(
          'chat4-tts: 📝 Pending message added via Chat4 Store, new count:',
          chatMessagesStore.messages.length,
        )

        // 如果是直播场景或演唱会场景，将 actor 消息转换为弹幕
        if (
          (isLivingScene.value ||
            Chat4SceneUtils.isConcertScene(currentScene.value)) &&
          nextMessage.sender_type === 'actor'
        ) {
          const storyStore = useStoryStore()
          const newComment: Comment = {
            id: nextMessage.id,
            username:
              nextMessage.sender?.name ||
              storyStore.currentActor?.name ||
              'Streamer',
            content: nextMessage.content.text || nextMessage.content.html || '',
            type: 'streamer',
          }
          addLiveComment(newComment)
        }
      }
    }
  }

  const playNextTTSMessage = () => {
    if (
      ttsState.value.queue.length > 0 &&
      !ttsState.value.isSceneTransitioning
    ) {
      const nextMessage = ttsState.value.queue.shift()
      if (nextMessage) {
        console.log('Playing next TTS message from queue:', nextMessage.id)
        ttsState.value.currentMessage = nextMessage
        playTTSMessage(nextMessage)
      }
    } else if (ttsState.value.isSceneTransitioning) {
      console.log('Scene transitioning, delaying TTS playback')
    }
  }

  // 恢复TTS播放（场景切换完成后调用）
  const resumeTTSPlayback = () => {
    console.log('Resuming TTS playback after scene transition:', {
      queueLength: ttsState.value.queue.length,
      isPlaying: ttsState.value.isPlaying,
      isSceneTransitioning: ttsState.value.isSceneTransitioning,
      userQueueLength: userMessageQueue.value.length,
      pendingDisplayMessages: ttsState.value.pendingDisplayMessages.length,
    })

    // 如果已经不在场景切换状态，说明已经被处理过了，避免重复处理
    if (!ttsState.value.isSceneTransitioning) {
      console.log('TTS already resumed, skipping duplicate call')
      return
    }

    ttsState.value.isSceneTransitioning = false

    // 首先处理所有等待显示的消息
    const chatMessagesStore = useChatMessagesStore()
    while (ttsState.value.pendingDisplayMessages.length > 0) {
      const pendingMessage = ttsState.value.pendingDisplayMessages.shift()
      if (pendingMessage) {
        console.log(
          'Adding pending message to store after scene transition:',
          pendingMessage.id,
        )
        chatMessagesStore.messages.push(pendingMessage)

        // 如果是直播场景，将 actor 消息转换为弹幕
        if (isLivingScene.value && pendingMessage.sender_type === 'actor') {
          const storyStore = useStoryStore()
          const newComment: Comment = {
            id: pendingMessage.id,
            username:
              pendingMessage.sender?.name ||
              storyStore.currentActor?.name ||
              'Streamer',
            content:
              pendingMessage.content.text || pendingMessage.content.html || '',
            type: 'streamer',
          }
          addLiveComment(newComment)
        }
      }
    }

    // 如果没有正在播放的TTS且队列中有消息，开始播放
    if (!ttsState.value.isPlaying && ttsState.value.queue.length > 0) {
      playNextTTSMessage()
    }

    // 如果没有TTS消息要播放，处理用户消息队列
    if (
      !ttsState.value.isPlaying &&
      ttsState.value.queue.length === 0 &&
      userMessageQueue.value.length > 0
    ) {
      console.log(
        'No TTS messages, processing user message queue after scene transition',
      )
      handleUserMessageQueue()
    }
  }

  // ==================== 弹幕管理方法 ====================
  const addLiveComment = (comment: Comment) => {
    // 在直播场景和演唱会场景添加弹幕
    if (
      !isLivingScene.value &&
      !Chat4SceneUtils.isConcertScene(currentScene.value)
    ) {
      return
    }

    danmakuState.value.liveComments.push(comment)
    danmakuState.value.version++ // 递增版本号，强制触发响应式更新

    // 限制评论数量，保持性能
    if (danmakuState.value.liveComments.length > 50) {
      danmakuState.value.liveComments.shift()
    }
  }

  const processDanmakuEvent = (eventData: any) => {
    // 只在直播场景处理弹幕
    if (!isLivingScene.value) {
      console.log(
        'Skipping danmaku processing for non-living scene:',
        currentScene.value,
      )
      return
    }

    // 防抖处理
    if (danmakuState.value.debounceTimer) {
      clearTimeout(danmakuState.value.debounceTimer)
    }

    danmakuState.value.debounceTimer = setTimeout(() => {
      processDanmakuData(eventData)
      danmakuState.value.debounceTimer = null
    }, 500)
  }

  const processDanmakuData = (eventData: any) => {
    try {
      const { names = [], sentences = [] } = eventData.data || {}

      if (
        !Array.isArray(names) ||
        !Array.isArray(sentences) ||
        names.length === 0 ||
        sentences.length === 0
      ) {
        console.warn('Invalid danmaku data:', eventData)
        return
      }

      // 生成弹幕列表
      const danmakuList: Comment[] = []
      const availableSentences = [...sentences]

      names.forEach((name: string) => {
        if (availableSentences.length > 0) {
          const randomIndex = Math.floor(
            Math.random() * availableSentences.length,
          )
          const selectedSentence = availableSentences[randomIndex]
          availableSentences.splice(randomIndex, 1)

          danmakuList.push({
            id: `danmaku_${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 11)}`,
            username: name,
            content: selectedSentence,
            type: 'normal',
          })
        }
      })

      // 延迟插入弹幕
      let cumulativeDelay = 0
      danmakuList.forEach((danmaku) => {
        const interval = Math.random() * 2000 + 1000
        cumulativeDelay += interval

        const timer = setTimeout(() => {
          // 再次检查场景
          if (isLivingScene.value) {
            addLiveComment(danmaku)
          }
        }, cumulativeDelay)

        danmakuState.value.danmakuTimers.push(timer)
      })
    } catch (error) {
      console.error('Error processing danmaku data:', error)
    }
  }

  // ==================== 清理方法 ====================
  const clearSceneSpecificState = (sceneId: string) => {
    console.log('Clearing state for scene:', sceneId)

    const audioManager = useAudioManager()
    const chatResourcesStore = useChatResourcesStore()

    // 0. 停止视频组播放（如果正在播放）
    if (chatResourcesStore.isPlayingVideoGroup) {
      console.log('🛑 场景切换，停止视频组播放')
      chatResourcesStore.stopVideoGroup()
    }

    // 1. 停止所有TTS相关的音频播放
    console.log('Stopping all TTS audio for scene change')
    audioManager.stopTTS()

    // 2. 停止当前BGM播放（场景切换时停止BGM，等待新场景的play_audio事件）
    console.log('🛑 场景切换，停止当前BGM播放')
    audioManager.stopBgm()

    // 3. 清理TTS缓存中属于该场景的消息（防止播放旧场景的音频）
    // 注意：这里我们不清理所有缓存，只是停止播放，因为缓存可以复用

    // 4. 清理TTS队列中属于该场景的消息
    const originalQueueLength = ttsState.value.queue.length
    ttsState.value.queue = ttsState.value.queue.filter(
      (msg) => msg.sceneId !== sceneId,
    )
    console.log(
      `Filtered TTS queue: ${originalQueueLength} -> ${ttsState.value.queue.length}`,
    )

    // 4. 清理等待显示的消息
    ttsState.value.pendingDisplayMessages = []

    // 5. 重置TTS播放状态
    if (
      ttsState.value.currentMessage?.sceneId === sceneId ||
      ttsState.value.isPlaying
    ) {
      console.log('Resetting TTS state due to scene change')
      ttsState.value.currentMessage = null
      ttsState.value.currentDisplayMessage = null
      ttsState.value.isPlaying = false
      ttsState.value.lastProcessedMessageId = null
    }

    // 注意：不在这里设置isSceneTransitioning = true，因为这个函数是在场景切换后调用的
    // 用于清理旧场景状态，而不是开始新的场景切换

    // 7. 清理弹幕定时器和弹幕数据
    clearDanmakuTimers()
    clearLiveComments()

    // 8. 清理用户消息队列
    userMessageQueue.value = []

    // 9. 清理自动发送定时器
    if (ttsState.value.autoSendTimer) {
      clearTimeout(ttsState.value.autoSendTimer)
      ttsState.value.autoSendTimer = null
    }

    console.log('Scene state cleared for:', sceneId)
  }

  const clearDanmakuTimers = () => {
    console.log(
      `Clearing ${danmakuState.value.danmakuTimers.length} danmaku timers`,
    )
    danmakuState.value.danmakuTimers.forEach((timer) => clearTimeout(timer))
    danmakuState.value.danmakuTimers = []

    if (danmakuState.value.debounceTimer) {
      clearTimeout(danmakuState.value.debounceTimer)
      danmakuState.value.debounceTimer = null
    }
  }

  const clearLiveComments = () => {
    danmakuState.value.liveComments = []
    danmakuState.value.version++ // 递增版本号，触发响应式更新
    console.log('Cleared all live comments')
  }

  const resetTTSState = () => {
    console.log('Manually resetting TTS state')
    ttsState.value.isPlaying = false
    ttsState.value.currentMessage = null
    ttsState.value.currentDisplayMessage = null
    console.log('TTS state reset completed')
  }

  // 定期检查TTS播放状态，确保与audioManager同步
  const statusCheckInterval = setInterval(checkTTSPlayingState, 100)

  // ==================== 用户消息管理 ====================

  // 发送消息回调函数（由外部设置）
  let sendQueuedMessage = (content: string) => {
    console.log('Default sendQueuedMessage called:', content)
  }

  const setSendMessageCallback = (callback: (content: string) => void) => {
    sendQueuedMessage = callback
  }

  const addUserMessage = (content: string) => {
    const userMessage = {
      id: `user_${Date.now()}`,
      content: content.trim(),
      timestamp: Date.now(),
    }

    // 检查是否在视频组播放期间
    const chatResourcesStore = useChatResourcesStore()
    if (chatResourcesStore.isPlayingVideoGroup) {
      // 视频组播放期间，使用视频组专用的消息处理
      const wasHandled = chatResourcesStore.handleUserInputDuringVideoGroup(
        userMessage.content,
      )
      if (wasHandled) {
        console.log('User message cached during video group playback:', content)
        return false // 表示消息被缓存，不应立即发送
      }
    }

    // 如果正在播放 TTS、队列中有消息或场景正在切换，将消息添加到队列
    if (
      ttsState.value.isPlaying ||
      ttsState.value.queue.length > 0 ||
      ttsState.value.isSceneTransitioning
    ) {
      userMessageQueue.value.push(userMessage.content)
      console.log('User message queued during TTS playback:', content)
      return false // 表示消息被缓存，不应立即发送
    }

    // 如果在等待自动发送期间收到新消息，取消定时器并添加到队列
    if (ttsState.value.autoSendTimer) {
      console.log(
        'New message received during auto-send delay, cancelling timer',
      )
      clearTimeout(ttsState.value.autoSendTimer)
      ttsState.value.autoSendTimer = null
      userMessageQueue.value.push(userMessage.content)

      // 重新启动 2 秒延迟
      handleUserMessageQueue()
      return false
    }

    // 如果没有播放 TTS、队列为空且没有场景切换，可以立即发送
    return true
  }

  const handleUserMessageQueue = () => {
    if (userMessageQueue.value.length === 0) {
      return
    }

    // 如果场景正在切换，延迟处理用户消息队列
    if (ttsState.value.isSceneTransitioning) {
      console.log(
        'Scene transitioning, deferring user message queue processing',
      )
      return
    }

    console.log('Setting up auto-send timer for queued messages')
    ttsState.value.autoSendTimer = setTimeout(() => {
      // 再次检查场景切换状态
      if (ttsState.value.isSceneTransitioning) {
        console.log(
          'Scene transitioning during auto-send delay, deferring message send',
        )
        return
      }

      if (userMessageQueue.value.length > 0) {
        const lastMessage =
          userMessageQueue.value[userMessageQueue.value.length - 1]
        userMessageQueue.value = []
        ttsState.value.autoSendTimer = null

        console.log('Auto-sending queued message:', lastMessage)
        sendQueuedMessage(lastMessage)
      }
    }, 2000) // 2秒延迟
  }

  const forceLatestUserMessage = (): string | null => {
    if (ttsState.value.autoSendTimer) {
      clearTimeout(ttsState.value.autoSendTimer)
      ttsState.value.autoSendTimer = null
    }

    if (userMessageQueue.value.length > 0) {
      const lastMessage =
        userMessageQueue.value[userMessageQueue.value.length - 1]
      userMessageQueue.value = []
      return lastMessage
    }

    return null
  }

  // ==================== 场景行为菜单管理 ====================

  const updateSceneActionsMenu = (menus: any[]) => {
    sceneActionsMenu.value = menus.map((menu: any) => ({
      key: menu.key || '',
      label: menu.name || menu.label || '',
      icon: menu.key || menu.icon || '',
      imageUrl: menu.image_url || '',
      requirement: menu.requirement || null,
      value: menu.value || 0,
      // 为了兼容性，如果是 heart_value 要求，转换为 level
      level:
        menu.requirement === 'heart_value' ? menu.value : menu.level || null,
    }))

    updateMapLocations(menus)
  }

  // ==================== 地图位置管理方法 ====================
  const updateMapLocations = (menus: any[]) => {
    mapLocations.value = menus.map((menu: any) => ({
      id: menu.key || menu.id || '',
      name: menu.name || menu.label || '',
      description: menu.description || '',
      image: menu.image_url || menu.imageUrl || '',
      position: menu.position || { x: 0, y: 0 }, // 服务器新增的位置信息
      isLocked: menu.requirement ? true : false,
      unlockCondition: menu.requirement
        ? {
            requiredLevel:
              menu.requirement === 'heart_value'
                ? `level${Math.floor(menu.value / 50)}`
                : undefined,
            requiredHeartValue:
              menu.requirement === 'heart_value' ? menu.value : undefined,
            requiredCoins:
              menu.requirement === 'coins' ? menu.value : undefined,
          }
        : undefined,
      meetupScene: menu.key || menu.id || '', // 直接使用服务器返回的key
      isNew: menu.isNew || false,
    }))
  }

  // ==================== 解锁状态管理方法 ====================

  const requestUnlockBlur = (tag: string, requiredHeartValue: number) => {
    console.log('Request unlock blur:', { tag, requiredHeartValue })
    unlockState.value.unlockTag = tag
    unlockState.value.requiredHeartValue = requiredHeartValue
    unlockState.value.paymentCoins = requiredHeartValue
    unlockState.value.showPaymentConfirmModal = true
  }

  const requestFavorabilityPayment = (coins: number) => {
    unlockState.value.unlockTag = 'favorability'
    unlockState.value.requiredHeartValue = 0
    unlockState.value.paymentCoins = coins
    unlockState.value.showPaymentConfirmModal = true
  }

  const closePaymentConfirmModal = () => {
    unlockState.value.showPaymentConfirmModal = false
    unlockState.value.paymentCoins = 0
    unlockState.value.unlockTag = ''
    unlockState.value.requiredHeartValue = 0
  }

  const confirmPayment = async () => {
    // 这里可以添加实际的支付逻辑
    console.log('Payment confirmed for unlock:', {
      tag: unlockState.value.unlockTag,
      amount: unlockState.value.paymentCoins,
    })

    // 关闭弹窗
    closePaymentConfirmModal()

    // 可以在这里触发解锁逻辑或者返回结果给调用方
    return true
  }

  // ==================== 朋友圈管理方法 ====================
  const updateMomentPosts = (data: ShowMomentsEventData) => {
    console.log('Updating moment posts:', data)

    if (data.moments && Array.isArray(data.moments)) {
      // 转换后端数据格式为前端格式
      const convertedPosts = data.moments.map((moment: any, index: number) => ({
        id: moment.content.id, // 使用后端返回的真实ID
        user: {
          id: 'user1',
          name: 'Character', // 这里可以从其他store获取角色名
          avatar: '', // 这里可以从其他store获取角色头像
        },
        content: moment.content,
        timestamp: Date.now() - (index + 1) * 3600000, // 模拟时间
        likeCount: moment.content.liked ? 1 : 0,
        commentCount: moment.content.comments?.length || 0,
        isLiked: moment.content.liked,
        isExpanded: false,
      }))

      momentState.value.posts = convertedPosts
      momentState.value.lastUpdated = Date.now()
      momentState.value.error = null

      // 保存个性签名
      if (data.personalized_signature) {
        momentState.value.personalizedSignature = data.personalized_signature
      }
    }
  }

  const setMomentLoading = (loading: boolean) => {
    momentState.value.isLoading = loading
  }

  const setMomentError = (error: string | null) => {
    momentState.value.error = error
  }

  const clearMomentPosts = () => {
    momentState.value.posts = []
    momentState.value.lastUpdated = null
    momentState.value.error = null
  }

  // ==================== 日记管理方法 ====================
  const updateDiaryData = (data: any) => {
    console.log('Updating diary data:', data)

    diaryState.value.content = data.content || null
    diaryState.value.date = data.date || null
    diaryState.value.emotion = data.emotion || null
    diaryState.value.status = data.status || null
    diaryState.value.region = data.region || null
    diaryState.value.lastUpdated = Date.now()
    diaryState.value.error = null
  }

  const setDiaryLoading = (loading: boolean) => {
    diaryState.value.isLoading = loading
  }

  const setDiaryError = (error: string | null) => {
    diaryState.value.error = error
  }

  const clearDiaryData = () => {
    diaryState.value.content = null
    diaryState.value.date = null
    diaryState.value.emotion = null
    diaryState.value.status = null
    diaryState.value.region = null
    diaryState.value.lastUpdated = null
    diaryState.value.error = null
  }

  const resetAllState = () => {
    console.log('Resetting all Chat4 state')

    // 清理定时器
    clearInterval(statusCheckInterval)

    // 停止所有TTS播放和请求
    const audioManager = useAudioManager()
    console.log('Stopping all TTS for state reset')
    audioManager.stopTTS()

    // 重置所有状态
    ttsState.value = {
      isPlaying: false,
      currentMessage: null,
      currentDisplayMessage: null,
      queue: [],
      pendingDisplayMessages: [],
      lastProcessedMessageId: null,
      autoSendTimer: null,
      isSceneTransitioning: false,
    }

    danmakuState.value = {
      liveComments: [],
      danmakuTimers: [],
      debounceTimer: null,
      version: 0,
    }

    momentState.value = {
      posts: [],
      isLoading: false,
      error: null,
      lastUpdated: null,
      personalizedSignature: null,
    }

    diaryState.value = {
      content: null,
      date: null,
      emotion: null,
      status: null,
      region: null,
      isLoading: false,
      error: null,
      lastUpdated: null,
    }

    userMessageQueue.value = []

    sceneState.value = {
      currentScene: null,
      previousScene: null,
      isTransitioning: false,
      isLoading: false,
      loadingType: null,
      error: null,
    }
  }

  return {
    // 状态
    sceneState: readonly(sceneState),
    ttsState: readonly(ttsState),
    danmakuState: readonly(danmakuState),
    momentState: readonly(momentState),
    diaryState: readonly(diaryState),
    userMessageQueue: readonly(userMessageQueue),

    // 计算属性
    currentScene,
    isLivingScene,
    isChatRoomScene,
    isVideoCallScene,
    isMeetupScene,
    isDancingScene,
    isMonitorScene,

    // 场景管理
    setCurrentScene,
    setSceneTransitioning,
    setSceneLoading,
    setSceneError,

    // TTS管理
    addTTSMessage,
    handleTTSPlaybackEnd,
    resumeTTSPlayback,

    // 弹幕管理
    addLiveComment,
    processDanmakuEvent,

    // 朋友圈管理
    updateMomentPosts,
    setMomentLoading,
    setMomentError,
    clearMomentPosts,

    // 日记管理
    updateDiaryData,
    setDiaryLoading,
    setDiaryError,
    clearDiaryData,

    // 用户消息
    addUserMessage,
    getLatestUserMessage: () => {
      return userMessageQueue.value[userMessageQueue.value.length - 1] || null
    },
    clearUserMessageQueue: () => {
      userMessageQueue.value = []
      if (ttsState.value.autoSendTimer) {
        clearTimeout(ttsState.value.autoSendTimer)
        ttsState.value.autoSendTimer = null
      }
    },
    forceLatestUserMessage,
    setSendMessageCallback,

    // 场景行为菜单
    sceneActionsMenu: readonly(sceneActionsMenu),
    updateSceneActionsMenu,

    // 地图位置管理
    mapLocations: readonly(mapLocations),
    updateMapLocations,

    // 解锁状态管理
    unlockState: readonly(unlockState),
    requestUnlockBlur,
    requestFavorabilityPayment,
    closePaymentConfirmModal,
    confirmPayment,

    // 清理方法
    clearSceneSpecificState,
    clearDanmakuTimers,
    clearLiveComments,
    resetTTSState,
    resetAllState,
  }
})
